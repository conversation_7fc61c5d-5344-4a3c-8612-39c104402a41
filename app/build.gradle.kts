plugins {
  alias(libs.plugins.android.application)
  alias(libs.plugins.kotlin.android)
  alias(libs.plugins.kotlin.compose)
  alias(libs.plugins.kotlin.parcelize)
  alias(libs.plugins.kotlin.serialization)
  alias(libs.plugins.ksp)
  alias(libs.plugins.lumo)
}

android {
  namespace = "com.example.shorts"
  compileSdk = 36

  defaultConfig {
    applicationId = "com.example.shorts"
    minSdk = 26
    targetSdk = 36
    versionCode = 1
    versionName = "1.0"

    testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"

    ndk {
      abiFilters.addAll(setOf("arm64-v8a"))
    }
  }

  buildTypes {
    release {
      isMinifyEnabled = false
      proguardFiles(
        getDefaultProguardFile("proguard-android-optimize.txt"),
        "proguard-rules.pro"
      )
    }
  }
  compileOptions {
//    isCoreLibraryDesugaringEnabled = true
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
  }
  kotlin {
    jvmToolchain(17)
    sourceSets {
      all {
        languageSettings {
          optIn("androidx.compose.material3.ExperimentalMaterial3Api")
          optIn("androidx.compose.foundation.ExperimentalFoundationApi")
          optIn("androidx.compose.foundation.layout.ExperimentalLayoutApi")
          optIn("androidx.compose.ui.text.ExperimentalTextApi")
          optIn("com.google.accompanist.permissions.ExperimentalPermissionsApi")
          optIn("kotlinx.coroutines.DelicateCoroutinesApi")
          optIn("org.orbitmvi.orbit.annotation.OrbitExperimental")
          optIn("kotlinx.serialization.ExperimentalSerializationApi")
          optIn("kotlin.time.ExperimentalTime")
        }
      }
    }
  }
  buildFeatures {
    viewBinding = true
    compose = true
    buildConfig = true
  }
}

dependencies {
  // Dramaverse SDK dependencies
  implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.aar"))))
  implementation("com.squareup.okhttp3:okhttp:5.1.0")
  implementation("androidx.viewpager2:viewpager2:1.1.0")
  implementation("com.github.bumptech.glide:glide:5.0.4")
  implementation("com.google.android.gms:play-services-ads-identifier:18.2.0")
  implementation("org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.1.20")
  // video player
  implementation("com.bytedanceapi:ttsdk-player_standard:1.47.200.2")

  implementation(libs.androidx.cardview)

  implementation(libs.androidx.core.ktx)
  implementation(libs.androidx.lifecycle.runtime.ktx)
  implementation(libs.androidx.activity.compose)
  implementation(platform(libs.androidx.compose.bom))
  implementation(libs.androidx.compose.foundation.layout)

  implementation(libs.androidx.ui)
  implementation(libs.androidx.ui.graphics)
  implementation(libs.androidx.ui.tooling.preview)
  implementation(libs.androidx.material3)

  implementation(libs.haze)
  implementation(libs.haze.materials)
  implementation(libs.compose.effects)

  // AndroidX Lifecycle
  implementation(libs.androidx.lifecycle.process)
  implementation(libs.androidx.lifecycle.runtime.compose)
  implementation(libs.androidx.lifecycle.viewmodel.ktx)
  implementation(libs.androidx.lifecycle.viewmodel.compose)
  implementation(libs.androidx.startup.runtime)
  implementation(libs.androidx.ui.viewbinding)


  // 3rd Party Views
  implementation(libs.cookiebar2)

  // Image Loading
  implementation(libs.coil)
  implementation(libs.coil.compose)
  implementation(libs.coil.gif)
  implementation(libs.coil.network.okhttp)

  // Navigation
  implementation(libs.guia)

  // Logging
  implementation(libs.kermit)

  // Dependency Injection
  implementation(platform(libs.koin.bom))
  implementation(libs.koin.core)
  implementation(libs.koin.android)
  implementation(libs.koin.androidx.compose)
  implementation(libs.koin.annotations)
  ksp(libs.insert.koin.koin.ksp.compiler)
  ksp(libs.koin.ksp.compiler)

  // Coroutines
  implementation(libs.kotlinx.coroutines.core)
  implementation(libs.kotlinx.coroutines.android)
  implementation(libs.kotlinx.datetime)
  implementation(libs.kotlinx.serialization.json)

  // Animations
  implementation(libs.lottie.compose)

  // Localization
  implementation(libs.lyricist)
  ksp(libs.lyricist.processor)

  // Key-Value Storage
  implementation(libs.mmkv)

  // State Management
  implementation(libs.orbit.compose)
  implementation(libs.orbit.viewmodel)

  // Lifecycle Management
  implementation(libs.resaca)
  implementation(libs.resacakoin)

  testImplementation(libs.junit)
  androidTestImplementation(libs.androidx.junit)
  androidTestImplementation(libs.androidx.espresso.core)
  androidTestImplementation(platform(libs.androidx.compose.bom))
  androidTestImplementation(libs.androidx.ui.test.junit4)
  debugImplementation(libs.androidx.ui.tooling)
  debugImplementation(libs.androidx.ui.test.manifest)
}
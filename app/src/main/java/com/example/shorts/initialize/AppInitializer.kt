package com.example.shorts.initialize

import android.content.Context
import androidx.startup.Initializer
import com.bytedance.sdk.shortplay.api.PSSDK
import com.example.shorts.foundation.dramaplayer.DramaSdkKey
import com.example.shorts.foundation.kermit.debugLog
import org.koin.core.context.GlobalContext

@Suppress("ObjectPropertyName")
private var _appContext: Context? = null
val appContext: Context
  get() = _appContext ?: GlobalContext.get().get<Context>()

class AppInitializer : Initializer<Unit> {
  override fun create(context: Context) {
    _appContext = context
    initializeDramaverseSDK(context)
  }

  private fun initializeDramaverseSDK(context: Context) {
    val builder = PSSDK.Config.Builder()
    builder.appId(DramaSdkKey.APP_ID)  // Replace with actual app ID from Dramaverse
      .vodAppId("568708")    // Replace with actual vod app ID from BytePlus
      .securityKey(DramaSdkKey.SECURITY_KEY) // Replace with actual security key from Dramaverse
      .licenseAssertPath("vod_player.lic") // Replace with actual license file path in assets
      .debug(true)

    PSSDK.init(context, builder.build()) { success, errorInfo ->
      debugLog(tag = "initializeDramaverseSDK") { "Dramaverse SDK onInitFinished: success = $success, errorInfo = $errorInfo" }
      if (success) {
        // Set eligible audience to true after successful initialization
        PSSDK.setEligibleAudience(true)
      }
    }
    debugLog(tag = "initializeDramaverseSDK") { "Dramaverse SDK initialization function created - waiting for AAR file" }
  }

  override fun dependencies(): List<Class<out Initializer<*>?>?> =
    listOf(KoinInitializer::class.java)
}
package com.example.shorts.ui.node.screen.wallet.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.shorts.R
import com.example.shorts.ui.node.screen.wallet.WalletBalance
import com.example.shorts.ui.theme.AppTheme
import java.math.BigDecimal

/**
 * 钱包余额卡片组件
 */
@Composable
fun WalletBalanceCard(
  balance: WalletBalance,
  modifier: Modifier = Modifier
) {
  Surface(
    modifier = modifier.fillMaxWidth(),
    shape = RoundedCornerShape(16.dp),
    color = Color(0xFF2F291B),
  ) {
    Box {
      Image(
        painter = painterResource(R.drawable.img_cash_item_background),
        contentDescription = null,
        modifier = Modifier.matchParentSize().alpha(.7f),
        contentScale = ContentScale.Crop
      )
      Row(
        modifier = Modifier
          .fillMaxWidth()
          .padding(horizontal = 18.dp, vertical = 28.dp),
        verticalAlignment = Alignment.CenterVertically
      ) {
        // 左侧钱包图标占位符
        Image(
          painter = painterResource(R.drawable.ic_cash_140px),
          contentDescription = null,
          modifier = Modifier.size(32.dp)
        )

        Spacer(modifier = Modifier.width(8.dp))

        Text(
          text = "${balance.currencySymbol}${balance.amount}",
          fontSize = 24.sp,
          fontWeight = FontWeight.Bold,
          modifier = Modifier.padding(top = 2.dp)
        )
      }
    }
  }
}

@Preview
@Composable
fun WalletBalanceCardPreview() {
  AppTheme {
    Box(
      modifier = Modifier
        .fillMaxWidth()
        .background(MaterialTheme.colorScheme.background)
        .padding(16.dp)
    ) {
      WalletBalanceCard(
        balance = WalletBalance(
          amount = BigDecimal("3464.25"),
          currencySymbol = "$",
          currencyCode = "USD"
        )
      )
    }
  }
}
